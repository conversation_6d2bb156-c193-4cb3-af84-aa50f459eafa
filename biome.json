{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto", "bracketSameLine": false, "bracketSpacing": true, "expand": "auto", "useEditorconfig": true}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noAdjacentSpacesInRegex": "error", "noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessEscapeInRegex": "error", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidBuiltinInstantiation": "error", "noInvalidConstructorSuper": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedPrivateClassMembers": "error", "noUnusedVariables": "error", "useIsNan": "error", "useValidForDirection": "error", "useValidTypeof": "error", "useYield": "error"}, "nursery": {"noConstantBinaryExpression": "error", "noFloatingPromises": "error", "noMisusedPromises": "error", "noShadow": "off", "noTsIgnore": "error", "noUselessBackrefInRegex": "error", "useUnifiedTypeSignature": "error"}, "style": {"noCommonJs": "error", "noInferrableTypes": "error", "noNamespace": "error", "useArrayLiterals": "error", "useAsConstAssertion": "error", "useBlockStatements": "error", "useConst": "error", "useNamingConvention": {"level": "error", "options": {"strictCase": false, "requireAscii": false, "conventions": [{"selector": {"kind": "variable"}, "match": "_?([^_]*)", "formats": ["camelCase", "PascalCase", "CONSTANT_CASE", "snake_case"]}, {"selector": {"kind": "objectLiteralProperty"}, "match": "_?([^_]*)", "formats": ["camelCase", "PascalCase", "CONSTANT_CASE", "snake_case"]}, {"selector": {"kind": "typeProperty"}, "match": "_?([^_]*)", "formats": ["camelCase", "PascalCase", "CONSTANT_CASE", "snake_case"]}, {"selector": {"kind": "typeMember"}, "match": "_?([^_]*)", "formats": ["camelCase", "PascalCase", "CONSTANT_CASE", "snake_case"]}]}}, "useThrowOnlyError": "error"}, "suspicious": {"noAssignInExpressions": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDoubleEquals": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateElseIf": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noEmptyInterface": "error", "noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noIrregularWhitespace": "off", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noSparseArray": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "noVar": "error", "noWith": "error", "useAdjacentOverloadSignatures": "error", "useAwait": "error", "useGetterReturn": "error", "useNamespaceKeyword": "error"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "asNeeded", "arrowParentheses": "asNeeded", "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto", "bracketSpacing": true}}, "html": {"formatter": {"selfCloseVoidElements": "always"}}, "overrides": [{"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["*.ts", "*.tsx", "*.mts", "*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}], "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}